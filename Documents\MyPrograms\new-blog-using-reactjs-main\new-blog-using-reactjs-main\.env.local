# OpenAI Configuration (for AI Blog Generator)
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBoj3Z44npK6jjEyyV0xF316RKdOK-eHMQ
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=ernestromelo-blog.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=ernestromelo-blog
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=ernestromelo-blog.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:6a2848df8590f29673c893

# EmailJS Configuration
NEXT_PUBLIC_EMAILJS_SERVICE_ID=service_3zc1f8b
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_CONTACT=template_d4kn1mr
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID_PROJECT=template_mdvd9pa
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=zf-TGiEd2hL1fRk4B

# Mailchimp Configuration
MAILCHIMP_API_KEY=*************************************
MAILCHIMP_AUDIENCE_ID=5376392e1e

# Admin Configuration
NEXT_PUBLIC_ADMIN_UID=KNlrg408xubJeEmwFpUbeDQWBgF3