// This file contains server-side OpenAI functions only
// Client-side components should import types from @/types instead

import OpenAI from 'openai'
import { GeneratedChapter, AIModelConfig, BlogOutline, ExternalSource, InternalLink } from '@/types'
import { DEFAULT_AI_MODELS } from '@/lib/ai-models'

// Initialize OpenAI client with error handling (server-side only)
let openai: OpenAI | null = null

// Initialize OpenAI client only when needed (server-side)
export const getOpenAIClient = () => {
  if (!openai && typeof window === 'undefined') {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || 'dummy-key',
    })
  }
  return openai
}

// Check if API key is available (server-side only)
const isOpenAIConfigured = () => {
  return typeof window === 'undefined' &&
         process.env.OPENAI_API_KEY &&
         process.env.OPENAI_API_KEY !== 'dummy-key'
}

// Re-export for backward compatibility
export { DEFAULT_AI_MODELS } from '@/lib/ai-models'

// Deep search for external sources
export async function searchExternalSources(
  keyword: string,
  model: string = DEFAULT_AI_MODELS.deepSearch,
  sessionId?: string
): Promise<ExternalSource[]> {
  // Check if OpenAI is configured
  if (!isOpenAIConfigured()) {
    console.warn('OpenAI API key not configured, returning fallback sources')
    return getFallbackSources(keyword)
  }

  try {
    const prompt = `You are a research assistant with access to comprehensive knowledge. Find 6-8 high-quality external sources related to "${keyword}" that would be valuable for external linking in a blog post written in Seth Godin's style.

Focus on finding sources from these categories:
- Academic studies and research papers (.edu domains, research institutions)
- Government data and statistics (.gov domains, official statistics)
- Surveys from reputable organizations (Pew Research, Gallup, McKinsey, etc.)
- Articles from established publications (Harvard Business Review, MIT Technology Review, etc.)
- Popular blogs from recognized experts and thought leaders
- Industry reports from credible organizations

For each source, provide:
1. A descriptive, engaging title that would work well in a blog post
2. A realistic URL from a known, credible domain
3. A brief description of why it's relevant and valuable for the topic
4. The type of source

IMPORTANT: Use realistic URLs from actual well-known domains like:
- harvard.edu, mit.edu, stanford.edu (for academic)
- pewresearch.org, gallup.com (for surveys)
- hbr.org, techcrunch.com, wired.com (for articles)
- mckinsey.com, deloitte.com (for industry reports)
- census.gov, bls.gov (for government data)

Return ONLY a valid JSON array with this exact structure:
[
  {
    "title": "Engaging Source Title",
    "url": "https://credible-domain.com/relevant-path",
    "description": "Brief explanation of relevance and value",
    "type": "study|data|survey|academic|blog|article"
  }
]`

    const client = getOpenAIClient()
    if (!client) throw new Error('OpenAI client not available')

    const response = await client.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    })

    // Track token usage if sessionId provided
    if (sessionId && response.usage) {
      const { tokenTracker } = await import('@/lib/token-tracker')
      tokenTracker.trackTokenUsage(
        sessionId,
        model,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
        'search'
      )
    }

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    // Clean the response to ensure it's valid JSON
    const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()

    try {
      const sources = JSON.parse(cleanContent)

      // Validate the structure
      if (!Array.isArray(sources)) {
        throw new Error('Response is not an array')
      }

      // Validate each source has required fields
      const validSources = sources.filter(source =>
        source.title &&
        source.url &&
        source.description &&
        source.type &&
        ['study', 'data', 'survey', 'academic', 'blog', 'article'].includes(source.type)
      )

      return validSources.slice(0, 8) // Limit to 8 sources max
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError)
      console.error('Raw content:', cleanContent)

      // Return fallback sources if parsing fails
      return getFallbackSources(keyword)
    }
  } catch (error) {
    console.error('Error searching external sources:', error)
    return getFallbackSources(keyword)
  }
}

// Fallback sources when API fails
function getFallbackSources(keyword: string): ExternalSource[] {
  return [
    {
      title: `Research Study on ${keyword}`,
      url: 'https://harvard.edu/research',
      description: 'Academic research providing insights into the topic',
      type: 'academic'
    },
    {
      title: `Industry Report: ${keyword} Trends`,
      url: 'https://mckinsey.com/insights',
      description: 'Comprehensive industry analysis and trends',
      type: 'article'
    },
    {
      title: `Survey Data on ${keyword}`,
      url: 'https://pewresearch.org/data',
      description: 'Statistical data and survey results',
      type: 'survey'
    }
  ]
}

// Generate blog outline and metadata
export async function generateBlogOutline(
  keyword: string,
  externalSources: ExternalSource[],
  internalLinks: InternalLink[],
  model: string = DEFAULT_AI_MODELS.outlineGeneration,
  sessionId?: string
): Promise<BlogOutline> {
  // Check if OpenAI is configured
  if (!isOpenAIConfigured()) {
    console.warn('OpenAI API key not configured, returning fallback outline')
    return getFallbackOutline(keyword)
  }

  try {
    const sourcesText = externalSources.length > 0
      ? externalSources.map(s => `- ${s.title}: ${s.description}`).join('\n')
      : 'No external sources available'

    const internalLinksText = internalLinks.length > 0
      ? internalLinks.map(l => `- ${l.title}: ${l.relevance}`).join('\n')
      : 'No internal links available'

    const prompt = `You are Seth Godin writing a blog post about "${keyword}". Create a compelling blog outline that captures his unique voice and perspective.

Seth Godin's writing style characteristics:
- Short, punchy sentences that pack a punch
- Conversational, direct tone
- Thought-provoking insights that challenge assumptions
- Stories and analogies that make complex ideas simple
- Challenges conventional thinking and status quo
- Focuses on human behavior, marketing psychology, and change
- Uses simple language but delivers profound concepts
- Makes readers think differently about familiar topics
- Often starts with a story or observation
- Ends with a call to action or thought-provoking question

STRICT RULES:
- NO em dashes (—) anywhere in any text
- NO colons (:) in the title
- Title should be 4-8 words, catchy and thought-provoking
- Create exactly 3 chapters that flow logically
- Each chapter should be substantial (500-800 words when written)
- Focus on insights, not just information
- Make it actionable and transformative

Available external sources for potential linking:
${sourcesText}

Available internal blog posts for potential linking:
${internalLinksText}

Create a blog outline that would make Seth Godin proud. Return ONLY a valid JSON object with this exact structure:

{
  "title": "Compelling title without colons (4-8 words)",
  "excerpt": "Hook the reader with 2-3 sentences that capture Seth's voice and make them want to read more",
  "tags": ["relevant", "seo", "friendly", "tags", "here"],
  "categories": ["primary-category", "secondary-category"],
  "chapters": [
    {
      "title": "Chapter 1 Title (engaging, not generic)",
      "description": "What this chapter accomplishes and why it matters",
      "keyPoints": ["specific insight 1", "specific insight 2", "specific insight 3"]
    },
    {
      "title": "Chapter 2 Title (builds on chapter 1)",
      "description": "How this chapter deepens the conversation",
      "keyPoints": ["specific insight 1", "specific insight 2", "specific insight 3"]
    },
    {
      "title": "Chapter 3 Title (brings it home)",
      "description": "The transformation or call to action this chapter delivers",
      "keyPoints": ["specific insight 1", "specific insight 2", "specific insight 3"]
    }
  ]
}`

    const client = getOpenAIClient()
    if (!client) throw new Error('OpenAI client not available')

    const response = await client.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8,
    })

    // Track token usage if sessionId provided
    if (sessionId && response.usage) {
      const { tokenTracker } = await import('@/lib/token-tracker')
      tokenTracker.trackTokenUsage(
        sessionId,
        model,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
        'outline'
      )
    }

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    // Clean the response to ensure it's valid JSON
    const cleanContent = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()

    try {
      const outline = JSON.parse(cleanContent)

      // Validate the structure
      if (!outline.title || !outline.excerpt || !outline.chapters || !Array.isArray(outline.chapters)) {
        throw new Error('Invalid outline structure')
      }

      // Ensure we have exactly 3 chapters
      if (outline.chapters.length !== 3) {
        throw new Error('Outline must have exactly 3 chapters')
      }

      // Validate each chapter has required fields
      for (const chapter of outline.chapters) {
        if (!chapter.title || !chapter.description || !chapter.keyPoints || !Array.isArray(chapter.keyPoints)) {
          throw new Error('Invalid chapter structure')
        }
      }

      // Ensure tags and categories are arrays
      outline.tags = Array.isArray(outline.tags) ? outline.tags : []
      outline.categories = Array.isArray(outline.categories) ? outline.categories : []

      return outline
    } catch (parseError) {
      console.error('Error parsing outline JSON:', parseError)
      console.error('Raw content:', cleanContent)

      // Return fallback outline
      return getFallbackOutline(keyword)
    }
  } catch (error) {
    console.error('Error generating blog outline:', error)
    return getFallbackOutline(keyword)
  }
}

// Fallback outline when API fails
function getFallbackOutline(keyword: string): BlogOutline {
  return {
    title: `The Truth About ${keyword}`,
    excerpt: `What everyone gets wrong about ${keyword} and why it matters more than you think.`,
    tags: [keyword.toLowerCase(), 'insights', 'strategy', 'change', 'perspective'],
    categories: ['insights', 'strategy'],
    chapters: [
      {
        title: 'The Problem Everyone Ignores',
        description: 'Why conventional wisdom about this topic is holding us back',
        keyPoints: ['Common misconceptions', 'Hidden costs of status quo', 'Why change is necessary']
      },
      {
        title: 'A Different Way Forward',
        description: 'Practical insights for thinking differently about the challenge',
        keyPoints: ['New perspective', 'Actionable strategies', 'Real-world examples']
      },
      {
        title: 'Making It Happen',
        description: 'How to implement these insights in your own context',
        keyPoints: ['First steps', 'Overcoming resistance', 'Measuring progress']
      }
    ]
  }
}

// Generate content for all chapters with context awareness
export async function generateChapters(
  outline: BlogOutline,
  externalSources: ExternalSource[],
  internalLinks: InternalLink[],
  model: string = DEFAULT_AI_MODELS.contentGeneration,
  sessionId?: string
): Promise<GeneratedChapter[]> {
  // Check if OpenAI is configured
  if (!isOpenAIConfigured()) {
    console.warn('OpenAI API key not configured, returning fallback chapters')
    return outline.chapters.map((chapter, index) => ({
      title: chapter.title,
      content: `## ${chapter.title}\n\n${chapter.description}\n\nKey points:\n${chapter.keyPoints.map(point => `- ${point}`).join('\n')}`,
      hasTable: false,
      hasQuote: false,
      hasCodeBlock: false
    }))
  }

  try {
    const chapters: GeneratedChapter[] = []

    // Generate all 3 chapters with full context
    for (let i = 0; i < outline.chapters.length; i++) {
      const chapter = outline.chapters[i]
      const previousChapters = chapters.slice(0, i)
      const nextChapters = outline.chapters.slice(i + 1)

      const generatedChapter = await generateSingleChapter(
        outline,
        chapter,
        i + 1,
        previousChapters,
        nextChapters,
        externalSources,
        internalLinks,
        model,
        sessionId
      )

      chapters.push(generatedChapter)
    }

    return chapters
  } catch (error) {
    console.error('Error generating chapters:', error)
    throw error
  }
}

// Generate a single chapter with full context
async function generateSingleChapter(
  outline: BlogOutline,
  chapterOutline: BlogOutline['chapters'][0],
  chapterNumber: number,
  previousChapters: GeneratedChapter[],
  nextChapters: BlogOutline['chapters'],
  externalSources: ExternalSource[],
  internalLinks: InternalLink[],
  model: string,
  sessionId?: string
): Promise<GeneratedChapter> {
  try {
    // Randomly decide what elements to include
    const includeTable = Math.random() < 0.4 // 40% chance
    const includeQuote = Math.random() < 0.3 // 30% chance
    const includeCodeBlock = Math.random() < 0.2 // 20% chance

    const previousContent = previousChapters.length > 0
      ? `Previous chapters covered:\n${previousChapters.map((ch, idx) => `Chapter ${idx + 1}: ${ch.title}\n${ch.content.substring(0, 200)}...`).join('\n\n')}`
      : 'This is the first chapter.'

    const nextContent = nextChapters.length > 0
      ? `Upcoming chapters will cover:\n${nextChapters.map((ch, idx) => `Chapter ${chapterNumber + idx + 1}: ${ch.title} - ${ch.description}`).join('\n')}`
      : 'This is the final chapter.'

    const sourcesText = externalSources.length > 0
      ? `External sources to potentially reference:\n${externalSources.map(s => `- ${s.title} (${s.url}): ${s.description}`).join('\n')}`
      : ''

    const internalLinksText = internalLinks.length > 0
      ? `Internal links to potentially include:\n${internalLinks.map(l => `- ${l.title} (${l.url}): ${l.relevance}`).join('\n')}`
      : ''

    const prompt = `You are Seth Godin writing Chapter ${chapterNumber} of a blog post titled "${outline.title}".

BLOG CONTEXT:
- Overall topic: ${outline.title}
- Blog excerpt: ${outline.excerpt}
- This chapter: ${chapterOutline.title}
- Chapter purpose: ${chapterOutline.description}
- Key points to cover: ${chapterOutline.keyPoints.join(', ')}

CHAPTER CONTEXT:
${previousContent}

${nextContent}

AVAILABLE RESOURCES:
${sourcesText}

${internalLinksText}

CONTENT REQUIREMENTS:
- Write in Seth Godin's distinctive voice and style
- 500-800 words for this chapter
- Short, punchy sentences
- Conversational and direct tone
- Include stories, analogies, or examples
- Challenge conventional thinking
- Focus on human behavior and insights
- NO em dashes (—) anywhere
- Use markdown formatting

SPECIAL ELEMENTS TO INCLUDE:
${includeTable ? '- Include a simple table that illustrates a key point (use markdown table format)' : ''}
${includeQuote ? '- Include a thought-provoking quote (use > blockquote format)' : ''}
${includeCodeBlock ? '- Include a code block or structured example (use ``` code block format)' : ''}

LINKING INSTRUCTIONS:
- Naturally incorporate 1-2 external links where they add value
- Include 1 internal link if relevant
- Make links feel natural, not forced
- Use descriptive link text, not "click here"

Write Chapter ${chapterNumber}: ${chapterOutline.title}

Return the content in markdown format. Start directly with the content, no title or chapter number.`

    const client = getOpenAIClient()
    if (!client) throw new Error('OpenAI client not available')

    const response = await client.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.8,
    })

    // Track token usage if sessionId provided
    if (sessionId && response.usage) {
      const { tokenTracker } = await import('@/lib/token-tracker')
      tokenTracker.trackTokenUsage(
        sessionId,
        model,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
        'content'
      )
    }

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    return {
      title: chapterOutline.title,
      content: content.trim(),
      hasTable: includeTable,
      hasQuote: includeQuote,
      hasCodeBlock: includeCodeBlock
    }
  } catch (error) {
    console.error(`Error generating chapter ${chapterNumber}:`, error)

    // Return fallback content
    return {
      title: chapterOutline.title,
      content: `## ${chapterOutline.title}\n\n${chapterOutline.description}\n\nKey points:\n${chapterOutline.keyPoints.map(point => `- ${point}`).join('\n')}`,
      hasTable: false,
      hasQuote: false,
      hasCodeBlock: false
    }
  }
}

// Generate image prompt for featured image
export async function generateImagePrompt(
  title: string,
  excerpt: string,
  model: string = DEFAULT_AI_MODELS.imagePrompt,
  sessionId?: string
): Promise<string> {
  // Check if OpenAI is configured
  if (!isOpenAIConfigured()) {
    console.warn('OpenAI API key not configured, returning fallback image prompt')
    return `A simple geometric shape in cartoon illustration style, pastel color palette with soft blues and pinks, minimalist design, centered composition, lots of white negative space, clean and simple`
  }

  try {
    const prompt = `You are an expert at creating DALL-E prompts for blog featured images. Create a prompt for a featured image for this blog post:

Title: "${title}"
Excerpt: "${excerpt}"

STYLE REQUIREMENTS (MUST FOLLOW EXACTLY):
- Pastel color palette only (soft pinks, light blues, gentle yellows, mint greens, lavender, etc.)
- Minimalist design with lots of negative/white space
- ONE main subject only, placed in the center
- Cartoon/illustration style, NOT photorealistic
- Clean, simple composition
- No text or words in the image
- No complex backgrounds or busy elements
- Focus on one key concept from the blog post

PROMPT STRUCTURE:
Start with the main subject, then specify the style requirements.

Example format:
"A [main subject] in cartoon illustration style, pastel color palette with soft [colors], minimalist design, centered composition, lots of white negative space, clean and simple"

Analyze the blog title and excerpt to identify the ONE most important visual concept, then create a DALL-E prompt that follows the style requirements exactly.

Return only the prompt text, nothing else.`

    const client = getOpenAIClient()
    if (!client) throw new Error('OpenAI client not available')

    const response = await client.chat.completions.create({
      model,
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7,
    })

    // Track token usage if sessionId provided
    if (sessionId && response.usage) {
      const { tokenTracker } = await import('@/lib/token-tracker')
      tokenTracker.trackTokenUsage(
        sessionId,
        model,
        response.usage.prompt_tokens,
        response.usage.completion_tokens,
        'image-prompt'
      )
    }

    const content = response.choices[0]?.message?.content
    if (!content) throw new Error('No response from OpenAI')

    return content.trim()
  } catch (error) {
    console.error('Error generating image prompt:', error)

    // Return fallback prompt
    return `A simple geometric shape in cartoon illustration style, pastel color palette with soft blues and pinks, minimalist design, centered composition, lots of white negative space, clean and simple`
  }
}

// Generate featured image using DALL-E
export async function generateFeaturedImage(
  title: string,
  excerpt: string,
  promptModel: string = DEFAULT_AI_MODELS.imagePrompt,
  imageModel: string = DEFAULT_AI_MODELS.imageGeneration,
  sessionId?: string
): Promise<string> {
  // Check if OpenAI is configured
  if (!isOpenAIConfigured()) {
    throw new Error('OpenAI API key is not configured. Cannot generate images.')
  }

  try {
    // First generate the prompt
    const imagePrompt = await generateImagePrompt(title, excerpt, promptModel, sessionId)

    // Then generate the image
    const client = getOpenAIClient()
    if (!client) throw new Error('OpenAI client not available')

    const response = await client.images.generate({
      model: imageModel,
      prompt: imagePrompt,
      n: 1,
      size: imageModel === 'dall-e-3' ? '1024x1024' : '512x512',
      quality: imageModel === 'dall-e-3' ? 'standard' : undefined,
      style: imageModel === 'dall-e-3' ? 'natural' : undefined,
    })

    // Track image usage if sessionId provided
    if (sessionId) {
      const { tokenTracker } = await import('@/lib/token-tracker')
      tokenTracker.trackImageUsage(sessionId, imageModel, 1, 'image-generation')
    }

    const imageUrl = response.data[0]?.url
    if (!imageUrl) throw new Error('No image URL returned from OpenAI')

    return imageUrl
  } catch (error) {
    console.error('Error generating featured image:', error)
    throw error
  }
}

// Note: Image upload to Firebase Storage would require a separate media upload API
// For now, we return the OpenAI image URL directly

// Note: OpenAI client is not exported to prevent client-side usage
// Use the API routes instead for client-side operations
